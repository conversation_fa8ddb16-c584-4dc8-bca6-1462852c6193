#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的视频分割工具测试版本
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import subprocess
import os

def check_gpu_support():
    '''检查NVIDIA GPU编码器支持情况'''
    try:
        print("正在检查NVIDIA GPU支持...")
        
        # 检查ffmpeg编码器支持
        result = subprocess.run('ffmpeg -hide_banner -encoders',
                              shell=True, capture_output=True, text=True,
                              encoding='utf-8', errors='ignore', timeout=10)
        
        if result.returncode == 0:
            output = result.stdout.lower()
            
            # 检查是否包含NVIDIA编码器
            if 'h264_nvenc' in output:
                print("✓ 检测到NVIDIA GPU编码器支持")
                return True
            else:
                print("✗ 未检测到NVIDIA GPU编码器")
                return False
        else:
            print(f"✗ ffmpeg编码器检查失败，返回码: {result.returncode}")
            return False

    except Exception as e:
        print(f"✗ 检查NVIDIA GPU支持时出错: {e}")
        return False

class SimpleVideoSplitter:
    def __init__(self, root):
        self.root = root
        self.root.title("简化视频分割工具")
        self.root.geometry("600x400")
        
        # 文件路径
        self.file_path = tk.StringVar()
        
        # GPU支持状态
        self.gpu_supported = False
        
        self.create_widgets()
        self.check_gpu_async()
    
    def create_widgets(self):
        # 文件选择
        file_frame = tk.Frame(self.root)
        file_frame.pack(pady=10, padx=10, fill='x')
        
        tk.Label(file_frame, text="视频文件:").pack(anchor='w')
        
        file_entry_frame = tk.Frame(file_frame)
        file_entry_frame.pack(fill='x', pady=5)
        
        tk.Entry(file_entry_frame, textvariable=self.file_path, width=50).pack(side='left', fill='x', expand=True)
        tk.Button(file_entry_frame, text="浏览", command=self.browse_file).pack(side='right', padx=(5,0))
        
        # 时间设置
        time_frame = tk.Frame(self.root)
        time_frame.pack(pady=10, padx=10, fill='x')
        
        tk.Label(time_frame, text="开始时间 (HH:MM:SS):").pack(anchor='w')
        self.start_time = tk.Entry(time_frame)
        self.start_time.pack(fill='x', pady=2)
        self.start_time.insert(0, "00:00:00")
        
        tk.Label(time_frame, text="结束时间 (HH:MM:SS):").pack(anchor='w', pady=(10,0))
        self.end_time = tk.Entry(time_frame)
        self.end_time.pack(fill='x', pady=2)
        self.end_time.insert(0, "00:01:00")
        
        # 处理模式
        mode_frame = tk.Frame(self.root)
        mode_frame.pack(pady=10, padx=10, fill='x')
        
        tk.Label(mode_frame, text="处理模式:").pack(anchor='w')
        
        self.mode_var = tk.IntVar(value=1)  # 默认CPU模式
        
        self.gpu_radio = tk.Radiobutton(mode_frame, text="GPU加速 (检查中...)", 
                                       variable=self.mode_var, value=0, state='disabled')
        self.gpu_radio.pack(anchor='w')
        
        tk.Radiobutton(mode_frame, text="CPU快速", 
                      variable=self.mode_var, value=1).pack(anchor='w')
        
        tk.Radiobutton(mode_frame, text="CPU平衡", 
                      variable=self.mode_var, value=2).pack(anchor='w')
        
        # 状态显示
        self.status_label = tk.Label(self.root, text="准备就绪", relief='sunken', anchor='w')
        self.status_label.pack(side='bottom', fill='x', padx=10, pady=5)
        
        # 分割按钮
        tk.Button(self.root, text="开始分割", command=self.split_video, 
                 bg='green', fg='white', font=('Arial', 12, 'bold')).pack(pady=20)
    
    def check_gpu_async(self):
        '''异步检查GPU支持'''
        import threading
        
        def check():
            self.gpu_supported = check_gpu_support()
            self.root.after(0, self.update_gpu_status)
        
        thread = threading.Thread(target=check)
        thread.daemon = True
        thread.start()
    
    def update_gpu_status(self):
        '''更新GPU状态显示'''
        if self.gpu_supported:
            self.gpu_radio.configure(text="GPU加速 ✓", state='normal', fg='green')
            self.mode_var.set(0)  # 默认选择GPU模式
            self.status_label.configure(text="GPU加速可用")
        else:
            self.gpu_radio.configure(text="GPU加速 ✗", state='disabled', fg='red')
            self.mode_var.set(1)  # 默认选择CPU模式
            self.status_label.configure(text="GPU不可用，使用CPU模式")
    
    def browse_file(self):
        '''浏览文件'''
        filename = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mkv *.mov *.wmv *.flv *.webm *.m4v"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.file_path.set(filename)
    
    def split_video(self):
        '''分割视频'''
        file_path = self.file_path.get().strip()
        start_time = self.start_time.get().strip()
        end_time = self.end_time.get().strip()
        
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", "请选择有效的视频文件")
            return
        
        if not start_time or not end_time:
            messagebox.showerror("错误", "请填写开始和结束时间")
            return
        
        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_dir = os.path.dirname(file_path)
        extension = os.path.splitext(file_path)[1]
        
        mode = self.mode_var.get()
        mode_names = ["gpu", "cpu_fast", "cpu_balanced"]
        output_file = os.path.join(output_dir, f"{base_name}_{mode_names[mode]}{extension}")
        
        # 生成ffmpeg命令
        if mode == 0 and self.gpu_supported:  # GPU模式
            cmd = f'ffmpeg -y -i "{file_path}" -ss {start_time} -to {end_time} -c:v h264_nvenc -preset fast -c:a aac "{output_file}"'
        elif mode == 1:  # CPU快速
            cmd = f'ffmpeg -y -i "{file_path}" -ss {start_time} -to {end_time} -c:v libx264 -preset ultrafast -crf 23 -c:a aac "{output_file}"'
        else:  # CPU平衡
            cmd = f'ffmpeg -y -i "{file_path}" -ss {start_time} -to {end_time} -c:v libx264 -preset medium -crf 22 -c:a aac "{output_file}"'
        
        self.status_label.configure(text="正在处理...")
        self.root.update()
        
        try:
            print(f"执行命令: {cmd}")
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.status_label.configure(text="分割完成！")
                messagebox.showinfo("成功", f"视频分割完成！\n输出文件: {output_file}")
            else:
                self.status_label.configure(text="分割失败")
                messagebox.showerror("错误", f"分割失败:\n{result.stderr}")
                
        except Exception as e:
            self.status_label.configure(text="分割失败")
            messagebox.showerror("错误", f"分割失败: {str(e)}")

def main():
    root = tk.Tk()
    app = SimpleVideoSplitter(root)
    root.mainloop()

if __name__ == "__main__":
    main()
