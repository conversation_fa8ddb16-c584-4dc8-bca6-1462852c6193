#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的GPU检测测试
"""

import subprocess

def check_gpu_support():
    '''检查NVIDIA GPU编码器支持情况'''
    gpu_support = {
        'nvidia': False
    }

    try:
        print("正在检查NVIDIA GPU支持...")
        
        # 方法1: 检查ffmpeg编码器支持
        result = subprocess.run('ffmpeg -hide_banner -encoders',
                              shell=True, capture_output=True, text=True,
                              encoding='utf-8', errors='ignore', timeout=10)
        
        if result.returncode == 0:
            output = result.stdout.lower()
            print(f"ffmpeg编码器检查完成，输出长度: {len(output)}")
            
            # 检查是否包含NVIDIA编码器
            if 'h264_nvenc' in output or 'nvenc' in output:
                print("检测到NVIDIA GPU编码器支持")
                gpu_support['nvidia'] = True
            else:
                print("未检测到NVIDIA GPU编码器")
                # 打印部分输出用于调试
                lines = output.split('\n')
                nvenc_lines = [line for line in lines if 'nvenc' in line or 'nvidia' in line]
                if nvenc_lines:
                    print(f"找到相关行: {nvenc_lines}")
        else:
            print(f"ffmpeg编码器检查失败，返回码: {result.returncode}")
            print(f"错误输出: {result.stderr}")

        # 方法2: 如果方法1失败，尝试直接测试NVIDIA编码器
        if not gpu_support['nvidia']:
            print("尝试直接测试NVIDIA编码器...")
            test_result = subprocess.run('ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 -c:v h264_nvenc -f null -',
                                       shell=True, capture_output=True, text=True,
                                       encoding='utf-8', errors='ignore', timeout=15)
            
            if test_result.returncode == 0:
                print("NVIDIA编码器测试成功")
                gpu_support['nvidia'] = True
            else:
                print(f"NVIDIA编码器测试失败: {test_result.stderr}")

    except subprocess.TimeoutExpired:
        print("GPU检查超时")
    except Exception as e:
        print(f"检查NVIDIA GPU支持时出错: {e}")
        import traceback
        traceback.print_exc()

    print(f"GPU支持检查结果: {gpu_support}")
    return gpu_support

if __name__ == "__main__":
    result = check_gpu_support()
    if result['nvidia']:
        print("\n🎉 您的系统支持NVIDIA GPU加速！")
    else:
        print("\n⚠️ 未检测到NVIDIA GPU支持，建议使用CPU模式")
