#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试GPU支持检测
"""

import subprocess
import sys

def check_ffmpeg_available():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run('ffmpeg -version', shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ ffmpeg 可用")
            return True
        else:
            print("✗ ffmpeg 不可用")
            return False
    except Exception as e:
        print(f"✗ ffmpeg 检查失败: {e}")
        return False

def check_nvidia_gpu():
    """检查NVIDIA GPU"""
    try:
        result = subprocess.run('nvidia-smi', shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ 检测到NVIDIA GPU")
            # 提取GPU信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'GeForce' in line or 'RTX' in line or 'GTX' in line:
                    print(f"  GPU: {line.strip()}")
            return True
        else:
            print("✗ 未检测到NVIDIA GPU")
            return False
    except Exception as e:
        print(f"✗ NVIDIA GPU 检查失败: {e}")
        return False

def check_ffmpeg_encoders():
    """检查ffmpeg编码器"""
    try:
        print("检查ffmpeg编码器...")
        result = subprocess.run('ffmpeg -hide_banner -encoders', shell=True, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            output = result.stdout.lower()
            
            # 检查各种编码器
            encoders = {
                'h264_nvenc': 'NVIDIA H.264',
                'hevc_nvenc': 'NVIDIA H.265',
                'h264_amf': 'AMD H.264',
                'hevc_amf': 'AMD H.265',
                'h264_qsv': 'Intel QuickSync H.264',
                'hevc_qsv': 'Intel QuickSync H.265'
            }
            
            found_encoders = []
            for encoder, name in encoders.items():
                if encoder in output:
                    found_encoders.append(f"✓ {name} ({encoder})")
                    print(f"  ✓ {name} ({encoder})")
            
            if not found_encoders:
                print("  ✗ 未找到硬件编码器")
                # 显示一些可用的编码器
                lines = output.split('\n')
                h264_lines = [line for line in lines if 'h264' in line and 'encoder' in line][:3]
                if h264_lines:
                    print("  可用的H.264编码器:")
                    for line in h264_lines:
                        print(f"    {line.strip()}")
            
            return len(found_encoders) > 0
        else:
            print(f"  ✗ 编码器检查失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 编码器检查失败: {e}")
        return False

def test_nvidia_encoding():
    """测试NVIDIA编码"""
    try:
        print("测试NVIDIA编码器...")
        cmd = 'ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 -c:v h264_nvenc -f null -'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("  ✓ NVIDIA编码器测试成功")
            return True
        else:
            print(f"  ✗ NVIDIA编码器测试失败")
            print(f"    错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ✗ NVIDIA编码器测试超时")
        return False
    except Exception as e:
        print(f"  ✗ NVIDIA编码器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("GPU支持检测测试")
    print("=" * 50)
    
    # 检查ffmpeg
    ffmpeg_ok = check_ffmpeg_available()
    print()
    
    if not ffmpeg_ok:
        print("ffmpeg不可用，无法进行进一步测试")
        return
    
    # 检查NVIDIA GPU
    nvidia_gpu = check_nvidia_gpu()
    print()
    
    # 检查编码器
    encoders_ok = check_ffmpeg_encoders()
    print()
    
    # 如果检测到NVIDIA相关编码器，进行实际测试
    if encoders_ok and nvidia_gpu:
        test_nvidia_encoding()
        print()
    
    # 总结
    print("总结:")
    print(f"  ffmpeg: {'✓' if ffmpeg_ok else '✗'}")
    print(f"  NVIDIA GPU: {'✓' if nvidia_gpu else '✗'}")
    print(f"  硬件编码器: {'✓' if encoders_ok else '✗'}")
    
    if ffmpeg_ok and nvidia_gpu and encoders_ok:
        print("\n🎉 GPU加速应该可以正常工作！")
    else:
        print("\n⚠️  建议使用CPU模式")

if __name__ == "__main__":
    main()
