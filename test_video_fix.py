#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试视频帧获取修复
"""

import cv2
import os
import tempfile
import subprocess

def test_opencv_frame_extraction(video_path, time_str="00:01:00.000"):
    """测试OpenCV帧提取方法"""
    print(f"测试OpenCV方法获取时间 {time_str} 的帧")
    
    try:
        # 打开视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("无法打开视频文件")
            return False
            
        # 获取视频属性
        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = frame_count / fps if fps > 0 else 0
        
        print(f"视频属性 - 帧数: {frame_count}, FPS: {fps}, 时长: {duration:.2f}秒")
        
        # 将时间转换为毫秒
        if '.' in time_str:
            main_time, milliseconds_str = time_str.split('.')
            h, m, s = map(int, main_time.split(':'))
            milliseconds = int(milliseconds_str)
            total_ms = h * 3600000 + m * 60000 + s * 1000 + milliseconds
        else:
            h, m, s = map(int, time_str.split(':'))
            total_ms = h * 3600000 + m * 60000 + s * 1000
            
        print(f"目标时间: {total_ms} 毫秒")
        
        # 检查时间是否超出视频长度
        if total_ms > duration * 1000:
            print(f"警告: 目标时间 {total_ms}ms 超出视频长度 {duration*1000:.0f}ms")
            total_ms = min(total_ms, duration * 1000 - 1000)  # 减去1秒作为缓冲
            print(f"调整为: {total_ms} 毫秒")
        
        # 设置视频位置
        success = cap.set(cv2.CAP_PROP_POS_MSEC, total_ms)
        if not success:
            print(f"无法设置视频位置到 {total_ms} 毫秒")
            cap.release()
            return False
            
        # 读取帧
        ret, frame = cap.read()
        if not ret or frame is None:
            print("无法读取帧")
            cap.release()
            return False
            
        print(f"成功获取帧，尺寸: {frame.shape}")
        cap.release()
        return True
        
    except Exception as e:
        print(f"OpenCV方法失败: {e}")
        return False

def test_ffmpeg_frame_extraction(video_path, time_str="00:01:00.000"):
    """测试ffmpeg帧提取方法"""
    print(f"测试ffmpeg方法获取时间 {time_str} 的帧")
    
    try:
        # 将时间转换为秒
        if '.' in time_str:
            main_time, milliseconds_str = time_str.split('.')
            h, m, s = map(int, main_time.split(':'))
            milliseconds = int(milliseconds_str)
            seconds = h * 3600 + m * 60 + s + milliseconds / 1000.0
        else:
            h, m, s = map(int, time_str.split(':'))
            seconds = h * 3600 + m * 60 + s
            
        print(f"目标时间: {seconds} 秒")
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        temp_file.close()
        
        try:
            # ffmpeg命令
            cmd = f'ffmpeg -y -ss {seconds:.3f} -i "{video_path}" -vframes 1 -q:v 2 "{temp_file.name}"'
            print(f"执行命令: {cmd}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                if os.path.exists(temp_file.name) and os.path.getsize(temp_file.name) > 0:
                    print(f"成功生成帧文件，大小: {os.path.getsize(temp_file.name)} 字节")
                    return True
                else:
                    print("ffmpeg未生成有效文件")
                    return False
            else:
                print(f"ffmpeg失败，返回码: {result.returncode}")
                print(f"错误输出: {result.stderr}")
                return False
                
        finally:
            # 清理临时文件
            try:
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)
            except:
                pass
                
    except Exception as e:
        print(f"ffmpeg方法失败: {e}")
        return False

def main():
    """主测试函数"""
    print("视频帧提取测试")
    print("=" * 50)
    
    # 请用户提供视频文件路径
    video_path = input("请输入视频文件路径（或按回车使用默认测试）: ").strip()
    
    if not video_path:
        print("未提供视频路径，请手动测试")
        return
        
    if not os.path.exists(video_path):
        print(f"视频文件不存在: {video_path}")
        return
        
    print(f"测试视频: {video_path}")
    print()
    
    # 测试不同时间点
    test_times = ["00:00:10.000", "00:01:00.000", "00:02:00.000"]
    
    for time_str in test_times:
        print(f"测试时间点: {time_str}")
        print("-" * 30)
        
        # 测试OpenCV方法
        opencv_success = test_opencv_frame_extraction(video_path, time_str)
        print(f"OpenCV方法: {'成功' if opencv_success else '失败'}")
        print()
        
        # 测试ffmpeg方法
        ffmpeg_success = test_ffmpeg_frame_extraction(video_path, time_str)
        print(f"ffmpeg方法: {'成功' if ffmpeg_success else '失败'}")
        print()
        print("=" * 50)

if __name__ == "__main__":
    main()
